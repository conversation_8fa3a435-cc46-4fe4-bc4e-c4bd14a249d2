using NeuroAnalysis, Statistics, StatsBase, FileIO, Images, Plots, LsqFit, FFTW, CSVFiles, DataFrames

# 添加sbxjoinhartleyFourier函数定义
function sbxjoinhartleyFourier(harts)
    cn = length(harts)
    uids = length(harts[1]["result"].cellId)
    kernsize = size(harts[1]["result"].kernnor[1])
    dataset = Dict()
    signifs=Dict();taumaxs=Dict();kstdmaxs=Dict();kdeltas=Dict();slambdas=Dict();
    orimaxs=Dict();sfmaxs=Dict();orimeans=Dict();sfmeans=Dict();
    kerns=Dict();kernraws=Dict();kernests=Dict();oris=Dict();oriLevels=Dict();
    orifits=Dict();sfLevels=Dict(); sffits=Dict();sfs=Dict();

    for u in 1:uids
        kern = Array{Float64}(undef,kernsize...,cn)  # Normlized kernels of all hartley
        kernraw = Array{Float64}(undef,kernsize...,cn)  # Raw kernels of all hartley
        kernest = Array{Float64}(undef,kernsize...,cn)  # Estimated kernels of all hartley
        signif = zeros(1,cn);taumax = zeros(1,cn);kstdmax = zeros(1,cn);
        kdelta = zeros(1,cn);slambda = zeros(1,cn);orimax = zeros(1,cn);
        sfmax = zeros(1,cn);orimean = zeros(1,cn);sfmean = zeros(1,cn);
        ori=[]; oriLevel=[]; sf=[]; sfLevel=[]; orifit=[]; sffit=[];

        for c in 1:cn
            signif[1,c] = harts[c]["result"].signif[u]
            taumax[1,c] = harts[c]["result"].taumax[u]
            kstdmax[1,c] = harts[c]["result"].kstdmax[u]
            kdelta[1,c] = harts[c]["result"].kdelta[u]
            slambda[1,c] = harts[c]["result"].slambda[u]
            orimax[1,c] = harts[c]["result"].orimax[u]
            sfmax[1,c] = harts[c]["result"].sfmax[u]
            orimean[1,c] = harts[c]["result"].orimean[u]
            sfmean[1,c] = harts[c]["result"].sfmean[u]

            kern[:,:,c] = harts[c]["result"].kernnor[u]
            kernraw[:,:,c] = harts[c]["result"].kernraw[u]
            kernest[:,:,c] = harts[c]["result"].kernest[u]

            θ = deg2rad.(harts[c]["result"].oriLevel[u])
            fr = harts[c]["result"].oriResp[u]

            push!(oriLevel,θ);
            push!(ori,fr);
            # fit von Mises for orientation
            fit=()
            try
                mfit = fitmodel(:vmn2,θ,fr)
                fit = (fit...,circtuningfeature(mfit,od=0.5π,fn=:o)...,vmn2=mfit)
            catch
            end
            push!(orifit,fit)

            fl = harts[c]["result"].sfLevel[u]
            fr = harts[c]["result"].sfResp[u]

            push!(sfLevel,fl);
            push!(sf,fr);
            # fit difference of gaussians
            fit=()
            try
                mfit = fitmodel(:dog,fl,fr)
                fit = (sftuningfeature(mfit)...,dog=mfit)
            catch
            end
            push!(sffit,fit)

        end
        signifs[u] = signif
        taumaxs[u] = taumax
        kstdmaxs[u] = kstdmax
        kdeltas[u] = kdelta
        slambdas[u] = slambda
        orimaxs[u] = orimax
        sfmaxs[u] = sfmax
        orimeans[u] = orimean
        sfmeans[u] = sfmean

        kerns[u] = kern
        kernraws[u] = kernraw
        kernests[u] = kernest

        oriLevels[u] = oriLevel
        oris[u] = ori
        orifits[u] = orifit
        sfLevels[u] = sfLevel
        sfs[u] = sf
        sffits[u] = sffit
    end
    dataset["signif"] = signifs
    dataset["taumax"] = taumaxs
    dataset["kstdmax"] = kstdmaxs
    dataset["kdelta"] = kdeltas
    dataset["slambda"] = slambdas
    dataset["orimax"] = orimaxs
    dataset["sfmax"] = sfmaxs
    dataset["orimean"] = orimeans
    dataset["sfmean"] = sfmeans

    dataset["kern"] = kerns
    dataset["kernraw"] = kernraws
    dataset["kernest"] = kernests

    dataset["oriraw"] = oris
    dataset["oriLevel"] = oriLevels
    dataset["orifit"] = orifits
    dataset["sfraw"] = sfs
    dataset["sfLevel"] = sfLevels
    dataset["sffit"] = sffits

    return dataset
end

## 测试脚本：检查和加载分别保存的JLD2文件

# 测试参数
test_base_path = "/media/vcl/vcl003/AF3/2P_analysis/U002"
test_sites = ["002_003_001", "002_004_001", "002_005_001", "002_006_001"]  # L, M, S, A
cone_types = ["L", "M", "S", "A"]

function check_fourier_results_structure(base_path, sites)
    """
    检查FourierRFresults目录结构和文件
    """
    println("="^60)
    println("检查FourierRFresults目录结构")
    println("="^60)

    all_files_found = true

    for (i, site) in enumerate(sites)
        fourier_dir = joinpath(base_path, site, "DataExport", "FourierRFresults")
        println("检查目录: $fourier_dir")

        if !isdir(fourier_dir)
            println("  ✗ 目录不存在")
            all_files_found = false
            continue
        end

        # 列出目录中的所有JLD2文件
        jld2_files = filter(f -> endswith(f, ".jld2"), readdir(fourier_dir))
        println("  找到 $(length(jld2_files)) 个JLD2文件:")

        for file in jld2_files
            println("    - $file")
        end

        if isempty(jld2_files)
            println("  ✗ 未找到JLD2文件")
            all_files_found = false
        end
    end

    return all_files_found
end

function load_separated_jld2_files(base_path, sites, cone_types)
    """
    加载分别保存的JLD2文件
    模拟load.(dataFile)的行为
    """
    println("="^60)
    println("加载分别保存的JLD2文件")
    println("="^60)

    loaded_data = []

    for (i, site) in enumerate(sites)
        fourier_dir = joinpath(base_path, site, "DataExport", "FourierRFresults")
        cone_type = cone_types[i]

        println("加载 $(cone_type) 条件数据 ($(site))...")

        if !isdir(fourier_dir)
            error("目录不存在: $fourier_dir")
        end

        # 查找主要的结果文件
        jld2_files = filter(f -> endswith(f, ".jld2"), readdir(fourier_dir))

        if isempty(jld2_files)
            error("在 $fourier_dir 中未找到JLD2文件")
        end

        # 尝试找到主要的结果文件（通常包含"result"）
        main_file = nothing
        for file in jld2_files
            if contains(file, "tuning_result") || contains(file, "result")
                main_file = joinpath(fourier_dir, file)
                break
            end
        end

        # 如果没找到主要文件，尝试加载第一个文件
        if main_file === nothing
            main_file = joinpath(fourier_dir, jld2_files[1])
            println("  警告: 未找到主要结果文件，使用: $(jld2_files[1])")
        end

        try
            # 加载主要文件
            data = load(main_file)
            println("  ✓ 成功加载主文件: $(basename(main_file))")

            # 检查是否需要加载其他分离的文件
            if !haskey(data, "result")
                println("  尝试重构result结构...")
                result = Dict()

                # 加载所有JLD2文件并合并
                for file in jld2_files
                    file_path = joinpath(fourier_dir, file)
                    file_data = load(file_path)
                    merge!(result, file_data)
                    println("    - 合并文件: $file")
                end

                data["result"] = result
            end

            # 验证必要字段
            required_fields = ["cellId", "kernnor", "signif", "taumax", "oriLevel", "oriResp", "sfLevel", "sfResp"]
            missing_fields = []

            for field in required_fields
                if !haskey(data["result"], field)
                    push!(missing_fields, field)
                end
            end

            if !isempty(missing_fields)
                println("  ✗ 缺少必要字段: $missing_fields")
                # 尝试从其他文件中查找缺失字段
                for file in jld2_files
                    file_path = joinpath(fourier_dir, file)
                    file_data = load(file_path)
                    for field in missing_fields
                        if haskey(file_data, field)
                            data["result"][field] = file_data[field]
                            println("    ✓ 从 $file 中找到字段: $field")
                        end
                    end
                end
            end

            push!(loaded_data, data)
            println("  ✓ $(cone_type) 条件数据加载完成")

        catch e
            println("  ✗ 加载失败: $e")
            rethrow(e)
        end
    end

    return loaded_data
end

function test_data_combination(loaded_data, cone_types)
    """
    测试数据合并功能
    """
    println("="^60)
    println("测试数据合并")
    println("="^60)

    try
        # 验证数据一致性
        println("验证数据一致性...")

        cell_counts = [length(data["result"].cellId) for data in loaded_data]
        println("  细胞数量: $cell_counts")

        if !all(x -> x == cell_counts[1], cell_counts)
            println("  ✗ 警告: 不同条件下的细胞数量不一致")
        else
            println("  ✓ 细胞数量一致")
        end

        # 检查kernel尺寸
        kernel_sizes = [size(data["result"].kernnor[1]) for data in loaded_data]
        println("  Kernel尺寸: $kernel_sizes")

        if !all(x -> x == kernel_sizes[1], kernel_sizes)
            error("  ✗ Kernel尺寸不一致")
        else
            println("  ✓ Kernel尺寸一致")
        end

        # 尝试合并数据
        println("尝试合并数据...")
        dataset = sbxjoinhartleyFourier(loaded_data)

        # 验证合并结果
        println("验证合并结果...")
        required_dataset_fields = ["kern", "signif", "taumax", "oriraw", "sfraw"]

        for field in required_dataset_fields
            if haskey(dataset, field)
                println("  ✓ 字段存在: $field")
            else
                println("  ✗ 字段缺失: $field")
            end
        end

        # 检查合并后的数据结构
        if haskey(dataset, "kern")
            cell_ids = collect(keys(dataset["kern"]))
            println("  合并后细胞数量: $(length(cell_ids))")
            println("  细胞ID范围: $(minimum(cell_ids)) - $(maximum(cell_ids))")

            # 检查第一个细胞的kernel尺寸
            first_cell = cell_ids[1]
            kern_size = size(dataset["kern"][first_cell])
            println("  合并后kernel尺寸: $kern_size")
            println("  锥细胞类型数量: $(kern_size[3])")
        end

        println("  ✓ 数据合并测试成功!")
        return dataset

    catch e
        println("  ✗ 数据合并测试失败: $e")
        rethrow(e)
    end
end

function run_jld2_loading_test(base_path=test_base_path, sites=test_sites, cone_types=cone_types)
    """
    运行完整的JLD2文件加载和合并测试
    """
    println("开始JLD2文件加载和合并测试")
    println("测试路径: $base_path")
    println("测试站点: $sites")
    println("锥细胞类型: $cone_types")
    println()

    try
        # 步骤1: 检查目录结构
        println("步骤1: 检查目录结构")
        structure_ok = check_fourier_results_structure(base_path, sites)

        if !structure_ok
            error("目录结构检查失败，请确认路径和文件存在")
        end
        println("✓ 目录结构检查通过")
        println()

        # 步骤2: 加载分离的JLD2文件
        println("步骤2: 加载分离的JLD2文件")
        loaded_data = load_separated_jld2_files(base_path, sites, cone_types)
        println("✓ 文件加载完成")
        println()

        # 步骤3: 测试数据合并
        println("步骤3: 测试数据合并")
        dataset = test_data_combination(loaded_data, cone_types)
        println("✓ 数据合并测试完成")
        println()

        # 步骤4: 保存测试结果
        println("步骤4: 保存测试结果")
        test_output_dir = joinpath(base_path, "_Summary", "DataExport", "test_results")
        isdir(test_output_dir) || mkpath(test_output_dir)

        test_output_file = joinpath(test_output_dir, "jld2_loading_test_dataset.jld2")
        try
            save(test_output_file, "dataset", dataset)
            println("✓ 测试结果已保存到: $test_output_file")
        catch e
            println("✗ 保存测试结果失败: $e")
            # 尝试分别保存关键组件
            try
                save(joinpath(test_output_dir, "test_kern.jld2"), "kern", dataset["kern"])
                save(joinpath(test_output_dir, "test_metadata.jld2"), "signif", dataset["signif"], "taumax", dataset["taumax"])
                println("✓ 测试结果已分别保存")
            catch e2
                println("✗ 分别保存也失败: $e2")
            end
        end

        println("="^60)
        println("JLD2文件加载和合并测试完成!")
        println("测试结果:")
        println("  - 成功加载 $(length(loaded_data)) 个条件的数据")
        println("  - 成功合并为统一数据集")
        println("  - 数据集包含 $(length(collect(keys(dataset["kern"])))) 个细胞")
        println("="^60)

        return dataset, loaded_data

    catch e
        println("="^60)
        println("✗ 测试失败: $e")
        println("="^60)
        rethrow(e)
    end
end

# 运行测试
println("开始运行JLD2文件加载测试...")
println("如果需要修改测试参数，请编辑文件顶部的test_base_path, test_sites, cone_types变量")
println()

# 执行测试
try
    dataset, loaded_data = run_jld2_loading_test()

    # 显示一些基本统计信息
    println("数据集基本信息:")
    if haskey(dataset, "kern")
        cell_ids = collect(keys(dataset["kern"]))
        println("  - 细胞总数: $(length(cell_ids))")

        if !isempty(cell_ids)
            first_cell = cell_ids[1]
            kern_shape = size(dataset["kern"][first_cell])
            println("  - Kernel尺寸: $(kern_shape[1])x$(kern_shape[2])")
            println("  - 锥细胞类型数: $(kern_shape[3])")
        end
    end

    println()
    println("测试成功完成! 您现在可以使用类似 load.(dataFile) 的方式加载数据。")

catch e
    println("测试执行失败: $e")
    println("请检查路径设置和文件是否存在。")
end